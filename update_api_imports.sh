#!/bin/bash

# List of files that need $api import updates
files=(
    "apps/ui/src/components/billing/auto-topup-settings.tsx"
    "apps/ui/src/components/billing/plan-management.tsx"
    "apps/ui/src/components/credits/payment-methods-management.tsx"
    "apps/ui/src/components/credits/top-up-credits-dialog.tsx"
    "apps/ui/src/components/dashboard/activity-chart.tsx"
    "apps/ui/src/components/dashboard/new-project-dialog.tsx"
    "apps/ui/src/components/onboarding/api-key-step.tsx"
    "apps/ui/src/components/onboarding/credits-step.tsx"
    "apps/ui/src/components/onboarding/provider-key-step.tsx"
    "apps/ui/src/components/provider-keys/create-provider-key-dialog.tsx"
    "apps/ui/src/components/provider-keys/provider-keys-list.tsx"
    "apps/ui/src/components/settings/caching-settings.tsx"
    "apps/ui/src/components/settings/organization-retention-settings.tsx"
    "apps/ui/src/components/settings/project-mode-settings.tsx"
    "apps/ui/src/components/shared/upgrade-to-pro-dialog.tsx"
    "apps/ui/src/components/usage/cache-rate-chart.tsx"
    "apps/ui/src/components/usage/cost-breakdown-chart.tsx"
    "apps/ui/src/components/usage/error-rate-chart.tsx"
    "apps/ui/src/components/usage/model-usage-table.tsx"
    "apps/ui/src/components/usage/usage-chart.tsx"
    "apps/ui/src/routes/dashboard/_layout/settings/transactions.tsx"
)

# Update each file
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "Updating $file"
        # Replace the import
        sed -i '' 's/import { \$api } from "@\/lib\/fetch-client";/import { useApi } from "@\/lib\/fetch-client";/g' "$file"
        echo "Updated import in $file"
    else
        echo "File not found: $file"
    fi
done

echo "All files updated!"
