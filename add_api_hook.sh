#!/bin/bash

# List of files that need the useApi hook added
files=(
    "apps/ui/src/components/billing/plan-management.tsx"
    "apps/ui/src/components/credits/payment-methods-management.tsx"
    "apps/ui/src/components/credits/top-up-credits-dialog.tsx"
    "apps/ui/src/components/dashboard/activity-chart.tsx"
    "apps/ui/src/components/dashboard/new-project-dialog.tsx"
    "apps/ui/src/components/onboarding/api-key-step.tsx"
    "apps/ui/src/components/onboarding/credits-step.tsx"
    "apps/ui/src/components/onboarding/provider-key-step.tsx"
    "apps/ui/src/components/provider-keys/create-provider-key-dialog.tsx"
    "apps/ui/src/components/provider-keys/provider-keys-list.tsx"
    "apps/ui/src/components/settings/caching-settings.tsx"
    "apps/ui/src/components/settings/organization-retention-settings.tsx"
    "apps/ui/src/components/settings/project-mode-settings.tsx"
    "apps/ui/src/components/shared/upgrade-to-pro-dialog.tsx"
    "apps/ui/src/components/usage/cache-rate-chart.tsx"
    "apps/ui/src/components/usage/cost-breakdown-chart.tsx"
    "apps/ui/src/components/usage/error-rate-chart.tsx"
    "apps/ui/src/components/usage/model-usage-table.tsx"
    "apps/ui/src/components/usage/usage-chart.tsx"
    "apps/ui/src/routes/dashboard/_layout/settings/transactions.tsx"
)

# Function to add useApi hook to a component
add_use_api_hook() {
    local file="$1"
    
    # Find the first line that uses $api and add the hook before it
    if grep -q '\$api\.' "$file"; then
        # Find the function declaration line
        func_line=$(grep -n "^export function\|^function\|^export default function" "$file" | head -1 | cut -d: -f1)
        
        if [ -n "$func_line" ]; then
            # Find the first line after the function declaration that doesn't start with whitespace + const/let/var
            # and insert the hook there
            insert_line=$((func_line + 1))
            
            # Check if the hook is already there
            if ! grep -q "const \$api = useApi();" "$file"; then
                # Find a good place to insert - after other hooks/const declarations
                while read -r line_num line_content; do
                    if [[ "$line_content" =~ ^[[:space:]]*(const|let|var)[[:space:]] ]] || [[ "$line_content" =~ ^[[:space:]]*const.*use[A-Z] ]]; then
                        insert_line=$((line_num + 1))
                    else
                        break
                    fi
                done < <(tail -n +$((func_line + 1)) "$file" | grep -n "." | head -10)
                
                # Insert the hook
                sed -i '' "${insert_line}i\\
\\	const \$api = useApi();\\
" "$file"
                echo "Added useApi hook to $file at line $insert_line"
            else
                echo "useApi hook already exists in $file"
            fi
        fi
    fi
}

# Update each file
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "Processing $file"
        add_use_api_hook "$file"
    else
        echo "File not found: $file"
    fi
done

echo "All files processed!"
